{"name": "worker", "version": "0.1.0", "private": true, "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint \"**/*.ts*\""}, "dependencies": {"@reality-scripts/lib": "*", "@supabase/supabase-js": "^2.39.0", "dotenv": "^16.3.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "node-fetch": "^3.3.2", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "tmp-promise": "^3.0.3"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^20.10.4", "eslint": "^8.56.0", "eslint-config-custom": "*", "ts-node-dev": "^2.0.0", "tsconfig": "*", "typescript": "^5.3.3"}}