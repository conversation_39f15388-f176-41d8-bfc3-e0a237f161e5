export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      videos: {
        Row: {
          id: string
          user_id: string
          storage_path: string
          locale: string
          expires_at: string | null
          status: "uploaded" | "processing" | "transcribed" | "processed" | "failed" | "expired"
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          storage_path: string
          locale?: string
          expires_at?: string | null
          status?: "uploaded" | "processing" | "transcribed" | "processed" | "failed" | "expired"
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          storage_path?: string
          locale?: string
          expires_at?: string | null
          status?: "uploaded" | "processing" | "transcribed" | "processed" | "failed" | "expired"
          created_at?: string
          updated_at?: string
        }
      }
      processing_jobs: {
        Row: {
          id: string
          video_id: string
          type: "transcribe" | "proxy" | "export" | "cleanup"
          status: "queued" | "processing" | "completed" | "failed"
          input: Json | null
          output: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          video_id: string
          type: "transcribe" | "proxy" | "export" | "cleanup"
          status?: "queued" | "processing" | "completed" | "failed"
          input?: Json | null
          output?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          type?: "transcribe" | "proxy" | "export" | "cleanup"
          status?: "queued" | "processing" | "completed" | "failed"
          input?: Json | null
          output?: Json | null
          created_at?: string
        }
      }
      transcriptions: {
        Row: {
          id: string
          video_id: string
          raw: Json
          text_clean: string
          words: Json
          language: string
          created_at: string
        }
        Insert: {
          id?: string
          video_id: string
          raw: Json
          text_clean: string
          words: Json
          language: string
          created_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          raw?: Json
          text_clean?: string
          words?: Json
          language?: string
          created_at?: string
        }
      }
      exports: {
        Row: {
          id: string
          video_id: string
          type: "srt" | "vtt" | "txt" | "json"
          storage_path: string
          created_at: string
        }
        Insert: {
          id?: string
          video_id: string
          type: "srt" | "vtt" | "txt" | "json"
          storage_path: string
          created_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          type?: "srt" | "vtt" | "txt" | "json"
          storage_path?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_expired_videos: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Enhanced type helpers
export type VideoStatus = Database["public"]["Tables"]["videos"]["Row"]["status"]
export type JobType = Database["public"]["Tables"]["processing_jobs"]["Row"]["type"]
export type JobStatus = Database["public"]["Tables"]["processing_jobs"]["Row"]["status"]
export type ExportType = Database["public"]["Tables"]["exports"]["Row"]["type"]

export interface VideoWithRelations extends Database["public"]["Tables"]["videos"]["Row"] {
  transcriptions?: Database["public"]["Tables"]["transcriptions"]["Row"][]
  exports?: Database["public"]["Tables"]["exports"]["Row"][]
  processing_jobs?: Database["public"]["Tables"]["processing_jobs"]["Row"][]
}

export interface TranscriptionWord {
  word: string
  start: number
  end: number
  confidence?: number
}

export interface TranscriptionData {
  text: string
  words: TranscriptionWord[]
  language: string
  duration?: number
}
