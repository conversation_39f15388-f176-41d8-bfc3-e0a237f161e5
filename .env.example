# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret

# ASR Configuration
HF_API_TOKEN=your-huggingface-token
WHISPER_MODEL=small
ASR_BACKEND=hf

# Application Configuration
LOG_LEVEL=info
NODE_ENV=development

# Optional Features
ENABLE_ANALYTICS=false
ENABLE_MONITORING=false

# Development Tools
ANALYZE=false

# CI/CD
CI=false

# Database (Auto-configured by Supabase)
POSTGRES_URL=
POSTGRES_PRISMA_URL=
POSTGRES_URL_NON_POOLING=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DATABASE=
POSTGRES_HOST=
